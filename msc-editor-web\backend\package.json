{"name": "msc-editor-backend", "version": "1.0.0", "description": "MSCEditor Web版本后端服务", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "build": "tsc", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["msc", "editor", "web", "savegame"], "author": "MSCEditor Web Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.1", "joi": "^17.11.0", "iconv-lite": "^0.6.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}, "engines": {"node": ">=16.0.0"}}