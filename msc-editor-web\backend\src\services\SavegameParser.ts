import fs from 'fs';
import path from 'path';
import {
  GameVariable,
  VariableType,
  VariableTypeIds,
  VariableHeader,
  FILE_CONSTANTS,
  ParseError,
  SaveGameInfo
} from '../../../shared/types';

/**
 * MSC存档解析器
 * 基于C++版本的ParseSavegame函数实现
 */
export class SavegameParser {
  private buffer: Buffer;
  private position: number = 0;
  private variables: GameVariable[] = [];
  private groups: string[] = [];
  private filePath: string;

  constructor(filePath: string) {
    this.filePath = filePath;
    this.buffer = fs.readFileSync(filePath);
    this.position = 0;
    this.variables = [];
    this.groups = [];
  }

  /**
   * 解析存档文件
   */
  public parse(): SaveGameInfo {
    this.position = 0;
    this.variables = [];
    this.groups = [];

    // 检查文件开头
    if (this.buffer.length === 0 || this.buffer[0] !== FILE_CONSTANTS.HX_STARTENTRY) {
      throw new ParseError(9, 0, '文件格式错误：缺少起始标记');
    }

    let emptyTagNum = 0;
    let variableIndex = 0;

    while (this.position < this.buffer.length) {
      try {
        const variable = this.parseEntry(variableIndex, emptyTagNum);
        if (variable) {
          this.variables.push(variable);
          variableIndex++;
          
          // 处理分组
          if (variable.rawKey && !this.groups.includes(variable.rawKey)) {
            this.groups.push(variable.rawKey);
          }
        }
      } catch (error) {
        if (error instanceof ParseError) {
          throw error;
        }
        break; // 到达文件末尾
      }
    }

    // 按key排序
    this.variables.sort((a, b) => a.key.localeCompare(b.key));

    const stats = fs.statSync(this.filePath);
    return {
      filename: path.basename(this.filePath),
      filepath: this.filePath,
      filesize: this.buffer.length,
      lastModified: stats.mtime,
      variables: this.variables,
      groups: this.groups
    };
  }

  /**
   * 解析单个条目
   */
  private parseEntry(index: number, emptyTagNum: number): GameVariable | null {
    const startPos = this.position;

    // 读取起始标记
    if (this.position >= this.buffer.length) return null;
    
    const startByte = this.buffer[this.position++];
    if (startByte !== FILE_CONSTANTS.HX_STARTENTRY) {
      throw new ParseError(9, startPos, `位置 ${startPos}: 期望起始标记 0x${FILE_CONSTANTS.HX_STARTENTRY.toString(16)}`);
    }

    // 读取标签大小
    if (this.position >= this.buffer.length) {
      throw new ParseError(43, this.position, '意外的文件结束：无法读取标签大小');
    }
    
    const tagSize = this.buffer[this.position++];

    // 读取标签字符串
    if (this.position + tagSize > this.buffer.length) {
      throw new ParseError(43, this.position, '意外的文件结束：无法读取标签字符串');
    }
    
    const tagBuffer = this.buffer.slice(this.position, this.position + tagSize);
    this.position += tagSize;
    
    // 转换标签为字符串 (UTF-8)
    const rawKey = tagBuffer.toString('utf8');

    // 读取值大小
    if (this.position + 4 > this.buffer.length) {
      throw new ParseError(32, this.position, '意外的文件结束：无法读取值大小');
    }
    
    const valueSize = this.buffer.readUInt32LE(this.position);
    this.position += 4;

    if (valueSize < 6) {
      throw new ParseError(32, this.position - 4, `值大小太小: ${valueSize}`);
    }

    // 读取值数据
    if (this.position + valueSize > this.buffer.length) {
      throw new ParseError(33, this.position, '意外的文件结束：无法读取值数据');
    }
    
    const valueBuffer = this.buffer.slice(this.position, this.position + valueSize);
    this.position += valueSize;

    // 检查结束标记
    if (valueBuffer[valueSize - 1] !== FILE_CONSTANTS.HX_ENDENTRY) {
      throw new ParseError(33, this.position - 1, '缺少结束标记');
    }

    // 解析头部信息
    const header = this.parseHeader(valueBuffer);
    
    // 提取实际值数据 (去除头部和结束标记)
    const actualValueBuffer = valueBuffer.slice(header.headerSize, valueSize - 1);
    
    // 格式化键名
    let key = rawKey;
    if (!key) {
      key = `untagged${emptyTagNum++}`;
    }
    
    const sanitizedKey = this.sanitizeKey(key);

    // 确定变量类型
    const type = this.getVariableType(header.valueType);

    return {
      id: `var_${index}`,
      key: sanitizedKey,
      rawKey: rawKey,
      value: actualValueBuffer.toString('binary'),
      staticValue: actualValueBuffer.toString('binary'),
      type: type,
      header: {
        containerType: header.containerType,
        keyType: header.keyType,
        valueType: header.valueType,
        keyProperty: header.keyProperty,
        valueProperty: header.valueProperty
      },
      group: 0, // 将在后续处理中设置
      position: index,
      flags: 0,
      displayValue: this.formatDisplayValue(actualValueBuffer, type)
    };
  }

  /**
   * 解析头部信息
   */
  private parseHeader(buffer: Buffer): any {
    let pos = 0;
    const header: any = { headerSize: 0 };

    // 读取容器类型 (如果存在)
    if (buffer.length > 4 && buffer[pos] !== 0xFF) {
      header.containerType = buffer.readUInt32LE(pos);
      pos += 4;
    }

    // 跳过分隔符
    if (pos < buffer.length && buffer[pos] === 0xFF) {
      pos++;
    }

    // 读取键类型 (如果存在)
    if (pos + 4 <= buffer.length) {
      const keyType = buffer.readUInt32LE(pos);
      if (keyType !== 0xFFFFFFFF) {
        header.keyType = keyType;
        pos += 4;
      }
    }

    // 读取值类型
    if (pos + 4 <= buffer.length) {
      header.valueType = buffer.readUInt32LE(pos);
      pos += 4;
    }

    header.headerSize = pos;
    return header;
  }

  /**
   * 根据类型ID获取变量类型
   */
  private getVariableType(typeId: number): VariableType {
    for (const [type, id] of Object.entries(VariableTypeIds)) {
      if (id === typeId) {
        return parseInt(type) as VariableType;
      }
    }
    return VariableType.Unknown;
  }

  /**
   * 清理键名
   */
  private sanitizeKey(key: string): string {
    return key.toLowerCase().trim();
  }

  /**
   * 格式化显示值
   */
  private formatDisplayValue(buffer: Buffer, type: VariableType): string {
    try {
      switch (type) {
        case VariableType.Integer:
          return buffer.length >= 4 ? buffer.readInt32LE(0).toString() : '0';
        
        case VariableType.Float:
          return buffer.length >= 4 ? buffer.readFloatLE(0).toFixed(6) : '0.0';
        
        case VariableType.Boolean:
          return buffer.length >= 1 ? (buffer[0] !== 0 ? 'true' : 'false') : 'false';
        
        case VariableType.String:
          return buffer.toString('utf8');
        
        case VariableType.Transform:
          if (buffer.length >= 40) { // 10 floats * 4 bytes
            const values = [];
            for (let i = 0; i < 10; i++) {
              values.push(buffer.readFloatLE(i * 4).toFixed(3));
            }
            return `Transform(${values.join(', ')})`;
          }
          return 'Transform(invalid)';
        
        case VariableType.Vector3:
          if (buffer.length >= 12) { // 3 floats * 4 bytes
            const x = buffer.readFloatLE(0).toFixed(3);
            const y = buffer.readFloatLE(4).toFixed(3);
            const z = buffer.readFloatLE(8).toFixed(3);
            return `Vector3(${x}, ${y}, ${z})`;
          }
          return 'Vector3(0, 0, 0)';
        
        default:
          return `Binary(${buffer.length} bytes)`;
      }
    } catch (error) {
      return `Error: ${error.message}`;
    }
  }

  /**
   * 获取解析后的变量列表
   */
  public getVariables(): GameVariable[] {
    return this.variables;
  }

  /**
   * 获取分组列表
   */
  public getGroups(): string[] {
    return this.groups;
  }
}
