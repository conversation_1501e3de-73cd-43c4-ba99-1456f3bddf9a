import React from 'react';
import { Layout as AntLayout, Menu, Typography, Space, Divider } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  EditOutlined,
  InfoCircleOutlined,
  CarOutlined,
  ToolOutlined,
  FileTextOutlined
} from '@ant-design/icons';

const { Header, Content, Footer } = AntLayout;
const { Title, Text } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/editor',
      icon: <EditOutlined />,
      label: '存档编辑器',
    },
    {
      key: '/about',
      icon: <InfoCircleOutlined />,
      label: '关于',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <AntLayout className="app-container">
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        padding: '0 24px',
        background: '#001529'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <CarOutlined style={{ fontSize: '24px', color: '#1890ff', marginRight: '12px' }} />
          <Title level={3} style={{ color: 'white', margin: 0 }}>
            MSCEditor Web
          </Title>
        </div>
        
        <Menu
          theme="dark"
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ 
            background: 'transparent',
            border: 'none',
            minWidth: '300px'
          }}
        />
      </Header>

      <Content className="main-content">
        {children}
      </Content>

      <Footer style={{ 
        textAlign: 'center', 
        background: '#f0f2f5',
        borderTop: '1px solid #d9d9d9',
        padding: '24px 50px'
      }}>
        <Space direction="vertical" size="small">
          <Space split={<Divider type="vertical" />}>
            <Space>
              <ToolOutlined />
              <Text strong>MSCEditor Web版本</Text>
            </Space>
            <Space>
              <FileTextOutlined />
              <Text>My Summer Car 存档编辑器</Text>
            </Space>
          </Space>
          
          <Text type="secondary" style={{ fontSize: '12px' }}>
            基于原版MSCEditor开发 | 支持所有存档编辑功能 | 
            <Text code>localhost:3000</Text>
          </Text>
          
          <Space size="large" style={{ marginTop: '8px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              🌐 跨平台Web界面
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              📁 文件上传/下载
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              🇨🇳 完整中文支持
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              🎮 物品生成 & 传送
            </Text>
          </Space>
        </Space>
      </Footer>
    </AntLayout>
  );
};

export default Layout;
