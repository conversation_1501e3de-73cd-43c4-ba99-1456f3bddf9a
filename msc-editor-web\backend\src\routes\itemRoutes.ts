import express from 'express';
import { ItemController } from '../controllers/ItemController';

const router = express.Router();
const itemController = new ItemController();

// 生成物品
router.post('/spawn', itemController.spawnItem);

// 传送对象
router.post('/teleport', itemController.teleportObject);

// 获取可生成的物品列表
router.get('/spawnable', itemController.getSpawnableItems);

// 获取可传送的对象列表
router.get('/teleportable', itemController.getTeleportableObjects);

export default router;
