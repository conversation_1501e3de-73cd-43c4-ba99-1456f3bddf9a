import React, { useState } from 'react';
import { 
  Card, 
  Upload, 
  Button, 
  Typography, 
  Space, 
  Row, 
  Col, 
  Alert,
  List,
  Tag,
  Divider,
  message
} from 'antd';
import { 
  InboxOutlined, 
  UploadOutlined, 
  FileTextOutlined,
  EditOutlined,
  CarOutlined,
  ToolOutlined,
  RocketOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { UploadProps } from 'antd';

const { Title, Paragraph, Text } = Typography;
const { Dragger } = Upload;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [uploading, setUploading] = useState(false);

  const features = [
    {
      icon: <FileTextOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: '存档解析',
      description: '完整支持EasySave2格式，精确解析游戏存档文件'
    },
    {
      icon: <EditOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      title: '变量编辑',
      description: '修改游戏变量，包括金钱、经验、物品状态等'
    },
    {
      icon: <RocketOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />,
      title: '物品生成',
      description: '在游戏中生成各种物品，支持自定义位置和数量'
    },
    {
      icon: <CarOutlined style={{ fontSize: '24px', color: '#eb2f96' }} />,
      title: '传送功能',
      description: '快速传送到游戏中的各个位置，节省时间'
    },
    {
      icon: <SafetyOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
      title: '自动备份',
      description: '修改前自动创建备份，确保数据安全'
    },
    {
      icon: <ToolOutlined style={{ fontSize: '24px', color: '#13c2c2' }} />,
      title: '中文支持',
      description: '完整的中文界面和配置，使用更加便捷'
    }
  ];

  const uploadProps: UploadProps = {
    name: 'savefile',
    multiple: false,
    action: '/api/v1/files/upload',
    accept: '.txt,.bak,',
    showUploadList: false,
    beforeUpload: (file) => {
      const isValidSize = file.size / 1024 / 1024 < 50; // 50MB
      if (!isValidSize) {
        message.error('文件大小不能超过50MB!');
        return false;
      }
      return true;
    },
    onChange: (info) => {
      const { status } = info.file;
      if (status === 'uploading') {
        setUploading(true);
      } else if (status === 'done') {
        setUploading(false);
        const response = info.file.response;
        if (response?.success) {
          message.success('文件上传成功!');
          // 跳转到编辑器页面
          navigate(`/editor/${response.data.fileId}`);
        } else {
          message.error(response?.error || '文件上传失败');
        }
      } else if (status === 'error') {
        setUploading(false);
        message.error('文件上传失败');
      }
    },
  };

  return (
    <div className="fade-in">
      {/* 欢迎区域 */}
      <Card className="content-card" style={{ marginBottom: '24px' }}>
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={16}>
            <Space direction="vertical" size="large">
              <div>
                <Title level={1} style={{ marginBottom: '8px' }}>
                  欢迎使用 MSCEditor Web版本
                </Title>
                <Paragraph style={{ fontSize: '16px', color: '#666' }}>
                  My Summer Car 存档编辑器的Web版本，提供与桌面版相同的功能，
                  支持在浏览器中直接编辑游戏存档文件。
                </Paragraph>
              </div>
              
              <Space wrap>
                <Tag color="blue">🌐 跨平台支持</Tag>
                <Tag color="green">📁 文件管理</Tag>
                <Tag color="orange">🎮 游戏功能</Tag>
                <Tag color="purple">🇨🇳 中文界面</Tag>
              </Space>
            </Space>
          </Col>
          
          <Col xs={24} md={8}>
            <div style={{ textAlign: 'center' }}>
              <CarOutlined style={{ fontSize: '120px', color: '#1890ff', opacity: 0.8 }} />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 文件上传区域 */}
      <Card className="content-card" style={{ marginBottom: '24px' }}>
        <Title level={3} style={{ marginBottom: '16px' }}>
          <UploadOutlined /> 上传存档文件
        </Title>
        
        <Alert
          message="支持的文件格式"
          description="支持MSC存档文件（通常无扩展名）、.txt文件和.bak备份文件。文件大小限制为50MB。"
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <Dragger {...uploadProps} className="upload-area">
          <p className="ant-upload-drag-icon">
            <InboxOutlined className="upload-icon" />
          </p>
          <p className="upload-text">
            点击或拖拽文件到此区域上传
          </p>
          <p className="upload-hint">
            支持MSC存档文件、.txt和.bak文件
          </p>
        </Dragger>

        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <Button 
            type="primary" 
            size="large"
            loading={uploading}
            onClick={() => navigate('/editor')}
          >
            或者直接进入编辑器
          </Button>
        </div>
      </Card>

      {/* 功能特性 */}
      <Card className="content-card">
        <Title level={3} style={{ marginBottom: '24px' }}>
          <ToolOutlined /> 功能特性
        </Title>
        
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card 
                size="small" 
                hoverable
                style={{ height: '100%' }}
                bodyStyle={{ padding: '16px' }}
              >
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center' }}>
                    {feature.icon}
                  </div>
                  <Title level={5} style={{ textAlign: 'center', margin: '8px 0' }}>
                    {feature.title}
                  </Title>
                  <Text type="secondary" style={{ textAlign: 'center', display: 'block' }}>
                    {feature.description}
                  </Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 使用说明 */}
      <Card className="content-card" style={{ marginTop: '24px' }}>
        <Title level={3} style={{ marginBottom: '16px' }}>
          📖 使用说明
        </Title>
        
        <List
          size="large"
          dataSource={[
            '上传您的MSC存档文件（通常位于游戏安装目录的SaveGames文件夹中）',
            '等待文件解析完成，系统会自动识别存档中的所有变量',
            '在编辑器中修改您需要的变量值，如金钱、经验等',
            '使用物品生成功能在游戏中添加物品',
            '使用传送功能快速移动到不同位置',
            '完成编辑后下载修改后的存档文件',
            '将文件替换回游戏目录即可生效'
          ]}
          renderItem={(item, index) => (
            <List.Item>
              <Text>
                <Text strong style={{ color: '#1890ff' }}>{index + 1}.</Text> {item}
              </Text>
            </List.Item>
          )}
        />
        
        <Divider />
        
        <Alert
          message="重要提示"
          description="建议在修改存档前备份原文件。本工具会自动创建备份，但手动备份更加安全。"
          type="warning"
          showIcon
        />
      </Card>
    </div>
  );
};

export default HomePage;
