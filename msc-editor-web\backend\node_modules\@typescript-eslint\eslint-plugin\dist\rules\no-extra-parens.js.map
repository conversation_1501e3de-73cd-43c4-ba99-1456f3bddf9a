{"version": 3, "file": "no-extra-parens.js", "sourceRoot": "", "sources": ["../../src/rules/no-extra-parens.ts"], "names": [], "mappings": ";AAAA,oEAAoE;AACpE,gGAAgG;;AAGhG,oDAA0D;AAC1D,wEAAsE;AAMtE,kCAA2E;AAC3E,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,iBAAiB,CAAC,CAAC;AAKtD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,+BAA+B,CAAC;QAC7C,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,kCAAkC;YAC/C,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,KAAK,CAAC;IACvB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,SAAS,CAChB,IAA4D;YAE5D,MAAM,IAAI,GAAG,KAAK,CAAC,gBAA4C,CAAC;YAEhE,wDAAwD;YACxD,MAAM,mBAAmB,GAAG,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,oBAAoB,GAAG,IAAA,sBAAe,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,mBAAmB,IAAI,oBAAoB,EAAE,CAAC;gBAChD,OAAO,CAAC,SAAS;YACnB,CAAC;YACD,IAAI,mBAAmB,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC;oBACV,GAAG,IAAI;oBACP,IAAI,EAAE;wBACJ,GAAG,IAAI,CAAC,IAAI;wBACZ,IAAI,EAAE,sBAAc,CAAC,kBAAyB;qBAC/C;iBACF,CAAC,CAAC;YACL,CAAC;YACD,IAAI,oBAAoB,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;oBACV,GAAG,IAAI;oBACP,KAAK,EAAE;wBACL,GAAG,IAAI,CAAC,KAAK;wBACb,IAAI,EAAE,sBAAc,CAAC,kBAAyB;qBAC/C;iBACF,CAAC,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,SAAS,OAAO,CACd,IAAsD;YAEtD,MAAM,IAAI,GAAG,KAAK,CAAC,cAA0C,CAAC;YAE9D,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,+EAA+E;gBAC/E,OAAO,IAAI,CAAC;oBACV,GAAG,IAAI;oBACP,MAAM,EAAE;wBACN,GAAG,IAAI,CAAC,MAAM;wBACd,IAAI,EAAE,sBAAc,CAAC,kBAAyB;qBAC/C;iBACF,CAAC,CAAC;YACL,CAAC;YAED,IACE,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;gBAC3B,qDAAqD;gBACrD,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,0BAAmB,CAAC;oBACxD,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,0BAAmB,CAAC,EACnE,CAAC;gBACD,OAAO,IAAI,CAAC;oBACV,GAAG,IAAI;oBACP,SAAS,EAAE;wBACT;4BACE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACpB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,SAAS,qBAAqB,CAC5B,IAA0D;YAE1D,MAAM,IAAI,GAAG,KAAK,CAAC,eAA2C,CAAC;YAE/D,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,+EAA+E;gBAC/E,OAAO,IAAI,CAAC;oBACV,GAAG,IAAI;oBACP,QAAQ,EAAE;wBACR,GAAG,IAAI,CAAC,QAAQ;wBAChB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;qBAC/C;iBACF,CAAC,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,SAAS,GAA0B;YACvC,kBAAkB;YAClB,uBAAuB,CAAC,IAAI;gBAC1B,IAAI,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,OAAO,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YACD,uBAAuB;YACvB,eAAe,CAAC,IAAI;gBAClB,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnC,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,eAAe,CAAC;wBAC3B,GAAG,IAAI;wBACP,QAAQ,EAAE;4BACR,GAAG,IAAI,CAAC,QAAQ;4BAChB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;YACD,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,OAAO;YACvB,gBAAgB,CAAC,IAAI;gBACnB,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;oBAC5D,OAAO,KAAK,CAAC,gBAAgB,CAAC;wBAC5B,GAAG,IAAI;wBACP,UAAU,EAAE;4BACV,GAAG,IAAI,CAAC,UAAU;4BAClB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;oBAC5D,OAAO,KAAK,CAAC,eAAe,CAAC;wBAC3B,GAAG,IAAI;wBACP,UAAU,EAAE;4BACV,GAAG,IAAI,CAAC,UAAU;4BAClB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;YACD,qBAAqB,CAAC,IAAI;gBACxB,+EAA+E;gBAC/E,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/B,OAAO,KAAK,CAAC,qBAAqB,CAAC;wBACjC,GAAG,IAAI;wBACP,IAAI,EAAE;4BACJ,GAAG,IAAI,CAAC,IAAI;4BACZ,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBACrC,OAAO,KAAK,CAAC,qBAAqB,CAAC;wBACjC,GAAG,IAAI;wBACP,UAAU,EAAE;4BACV,GAAG,IAAI,CAAC,UAAU;4BAClB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACpC,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,qBAAqB,CAAC;wBACjC,GAAG,IAAI;wBACP,SAAS,EAAE;4BACT,GAAG,IAAI,CAAC,SAAS;4BACjB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;YACD,mBAAmB;YACnB,gDAAgD;YAChD,YAAY,CAAC,IAAI;gBACf,uDAAuD;gBACvD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5C,OAAO,KAAK,CAAC,YAAY,CAAC;wBACxB,GAAG,IAAI;wBACP,IAAI,EAAE,IAAI;qBACX,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5C,OAAO,KAAK,CAAC,YAAY,CAAC;wBACxB,GAAG,IAAI;wBACP,IAAI,EAAE,IAAI;qBACX,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBAChD,OAAO,KAAK,CAAC,YAAY,CAAC;wBACxB,GAAG,IAAI;wBACP,MAAM,EAAE,IAAI;qBACb,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YACD,4BAA4B,CAAC,IAAmB;gBAC9C,IAAI,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,OAAO,KAAK,CAAC,4BAA4B,CAAC,CAAC,IAAI,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC;YACD,cAAc;YACd,iBAAiB,EAAE,SAAS;YAC5B,gBAAgB,CAAC,IAAI;gBACnB,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACjC,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,gBAAgB,CAAC;wBAC5B,GAAG,IAAI;wBACP,MAAM,EAAE;4BACN,GAAG,IAAI,CAAC,MAAM;4BACd,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,aAAa,EAAE,OAAO;YACtB,mBAAmB;YACnB,kBAAkB;YAClB,qBAAqB;YACrB,aAAa,CAAC,IAAI;gBAChB,IAAI,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACpC,OAAO,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;YACD,UAAU,CAAC,IAAI;gBACb,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC7C,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;YACD,kBAAkB;YAClB,cAAc,CAAC,IAAI;gBACjB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrD,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YACD,eAAe,EAAE,qBAAqB;YACtC,gBAAgB,EAAE,qBAAqB;YACvC,qBAAqB;YACrB,iBAAiB;YACjB,iGAAiG;YACjG,eAAe,CAAC,IAAI;gBAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;SACF,CAAC;QACF,uEAAuE;QACvE,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACjD,SAAS,CAAC,cAAc,GAAG,UAAU,IAAI;gBACvC,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChC,sEAAsE;oBACtE,mCAAmC;oBACnC,OAAO;gBACT,CAAC;gBAED,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;YACF,SAAS,CAAC,cAAc,GAAG,UAAU,IAAI;gBACvC,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChC,4CAA4C;oBAC5C,OAAO,KAAK,CAAC,cAAc,CAAC;wBAC1B,GAAG,IAAI;wBACP,IAAI,EAAE,sBAAc,CAAC,cAAc;wBACnC,KAAK,EAAE;4BACL,GAAG,IAAI,CAAC,KAAK;4BACb,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,gCAAgC,CAAC,GAAG,UAC5C,IAAuD;gBAEvD,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChC,4CAA4C;oBAC5C,OAAO,KAAK,CAAC,gCAAgC,CAAC,CAAC;wBAC7C,GAAG,IAAI;wBACP,IAAI,EAAE,sBAAc,CAAC,cAAqB;wBAC1C,KAAK,EAAE;4BACL,GAAG,IAAI,CAAC,KAAK;4BACb,IAAI,EAAE,sBAAc,CAAC,kBAAyB;yBAC/C;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,KAAK,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;CACF,CAAC,CAAC"}