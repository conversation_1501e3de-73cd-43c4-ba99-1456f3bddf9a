import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Card, 
  Tabs, 
  Typography, 
  Space, 
  Alert,
  Spin,
  Button,
  Upload,
  message
} from 'antd';
import { 
  EditOutlined, 
  UploadOutlined,
  FileTextOutlined,
  RocketOutlined,
  CarOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';

// 组件导入
import VariableEditor from '../components/VariableEditor';
import ItemSpawner from '../components/ItemSpawner';
import TeleportTool from '../components/TeleportTool';
import FileManager from '../components/FileManager';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const EditorPage: React.FC = () => {
  const { fileId } = useParams<{ fileId?: string }>();
  const [currentFileId, setCurrentFileId] = useState<string | null>(fileId || null);
  const [loading, setLoading] = useState(false);
  const [fileInfo, setFileInfo] = useState<any>(null);

  useEffect(() => {
    if (fileId) {
      setCurrentFileId(fileId);
      loadFileInfo(fileId);
    }
  }, [fileId]);

  const loadFileInfo = async (id: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/files/${id}`);
      const result = await response.json();
      
      if (result.success) {
        setFileInfo(result.data);
      } else {
        message.error(result.error || '获取文件信息失败');
      }
    } catch (error) {
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'savefile',
    multiple: false,
    action: '/api/v1/files/upload',
    accept: '.txt,.bak,',
    showUploadList: false,
    beforeUpload: (file) => {
      const isValidSize = file.size / 1024 / 1024 < 50;
      if (!isValidSize) {
        message.error('文件大小不能超过50MB!');
        return false;
      }
      return true;
    },
    onChange: (info) => {
      const { status } = info.file;
      if (status === 'uploading') {
        setLoading(true);
      } else if (status === 'done') {
        setLoading(false);
        const response = info.file.response;
        if (response?.success) {
          message.success('文件上传成功!');
          setCurrentFileId(response.data.fileId);
          loadFileInfo(response.data.fileId);
        } else {
          message.error(response?.error || '文件上传失败');
        }
      } else if (status === 'error') {
        setLoading(false);
        message.error('文件上传失败');
      }
    },
  };

  const handleDownload = () => {
    if (currentFileId) {
      const downloadUrl = `/api/v1/files/${currentFileId}/download`;
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileInfo?.filename || 'savegame';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text style={{ marginTop: '16px', display: 'block', textAlign: 'center' }}>
          正在处理文件...
        </Text>
      </div>
    );
  }

  if (!currentFileId) {
    return (
      <div className="fade-in">
        <Card className="content-card">
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <FileTextOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }} />
            <Title level={3}>请选择存档文件</Title>
            <Text type="secondary" style={{ marginBottom: '24px', display: 'block' }}>
              上传MSC存档文件开始编辑
            </Text>
            
            <Upload {...uploadProps}>
              <Button type="primary" size="large" icon={<UploadOutlined />}>
                上传存档文件
              </Button>
            </Upload>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="fade-in">
      {/* 文件信息栏 */}
      {fileInfo && (
        <Card className="content-card" style={{ marginBottom: '24px' }}>
          <div className="toolbar">
            <div className="toolbar-left">
              <Space>
                <FileTextOutlined style={{ fontSize: '16px', color: '#1890ff' }} />
                <Text strong>{fileInfo.filename}</Text>
                <Text type="secondary">
                  大小: {(fileInfo.size / 1024).toFixed(1)} KB
                </Text>
                <Text type="secondary">
                  修改时间: {new Date(fileInfo.lastModified).toLocaleString('zh-CN')}
                </Text>
              </Space>
            </div>
            
            <div className="toolbar-right">
              <Space>
                <Upload {...uploadProps}>
                  <Button icon={<UploadOutlined />}>
                    更换文件
                  </Button>
                </Upload>
                <Button 
                  type="primary" 
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                >
                  下载文件
                </Button>
              </Space>
            </div>
          </div>
        </Card>
      )}

      {/* 主要编辑区域 */}
      <Card className="content-card">
        <Tabs defaultActiveKey="variables" size="large">
          <TabPane 
            tab={
              <span>
                <EditOutlined />
                变量编辑
              </span>
            } 
            key="variables"
          >
            <Alert
              message="变量编辑"
              description="在这里可以查看和修改存档中的所有变量。请谨慎修改，建议先了解变量的作用。"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <VariableEditor fileId={currentFileId} />
          </TabPane>

          <TabPane 
            tab={
              <span>
                <RocketOutlined />
                物品生成
              </span>
            } 
            key="items"
          >
            <Alert
              message="物品生成"
              description="在游戏中生成各种物品。选择物品类型、位置和数量，然后点击生成。"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <ItemSpawner fileId={currentFileId} />
          </TabPane>

          <TabPane 
            tab={
              <span>
                <CarOutlined />
                传送工具
              </span>
            } 
            key="teleport"
          >
            <Alert
              message="传送工具"
              description="快速传送到游戏中的各个位置。选择要传送的对象和目标位置。"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <TeleportTool fileId={currentFileId} />
          </TabPane>

          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                文件管理
              </span>
            } 
            key="files"
          >
            <Alert
              message="文件管理"
              description="管理上传的存档文件，查看备份，恢复历史版本。"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <FileManager 
              currentFileId={currentFileId} 
              onFileChange={setCurrentFileId}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default EditorPage;
