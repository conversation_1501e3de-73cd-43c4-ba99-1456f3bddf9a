{"name": "msc-editor-frontend", "version": "1.0.0", "description": "MSCEditor Web版本前端应用", "private": true, "dependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.3.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "styled-components": "^6.1.6", "@types/styled-components": "^5.1.34", "dayjs": "^1.11.10", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "file-saver": "^2.0.5", "@types/file-saver": "^2.0.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "proxy": "http://localhost:3001"}