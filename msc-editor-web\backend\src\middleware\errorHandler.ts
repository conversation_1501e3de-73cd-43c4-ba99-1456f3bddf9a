import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../../../shared/types';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error('错误详情:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // 默认错误状态码
  let statusCode = error.statusCode || 500;
  let message = error.message || '服务器内部错误';

  // 处理特定类型的错误
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '请求参数验证失败';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = '无效的参数格式';
  } else if (error.name === 'MulterError') {
    statusCode = 400;
    if (error.message.includes('File too large')) {
      message = '文件大小超出限制';
    } else if (error.message.includes('Unexpected field')) {
      message = '不支持的文件字段';
    } else {
      message = '文件上传错误';
    }
  } else if (error.code === 'ENOENT') {
    statusCode = 404;
    message = '文件不存在';
  } else if (error.code === 'EACCES') {
    statusCode = 403;
    message = '文件访问权限不足';
  }

  // 在生产环境中隐藏敏感错误信息
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = '服务器内部错误';
  }

  const response: ApiResponse = {
    success: false,
    error: message
  };

  res.status(statusCode).json(response);
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export const createError = (message: string, statusCode: number = 500): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};
