import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import path from 'path';
import fs from 'fs';

// 路由导入
import fileRoutes from './routes/fileRoutes';
import savegameRoutes from './routes/savegameRoutes';
import configRoutes from './routes/configRoutes';
import itemRoutes from './routes/itemRoutes';

// 中间件导入
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';

const app = express();
const PORT = process.env.PORT || 3001;

// 确保上传目录存在
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 基础中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use(compression());
app.use(morgan('combined'));

// CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['http://localhost:3000'] 
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 请求解析中间件
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务
app.use('/uploads', express.static(uploadsDir));

// API路由
app.use('/api/v1/files', fileRoutes);
app.use('/api/v1/savegame', savegameRoutes);
app.use('/api/v1/config', configRoutes);
app.use('/api/v1/items', itemRoutes);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    service: 'MSCEditor Web Backend'
  });
});

// API信息端点
app.get('/api/v1', (req, res) => {
  res.json({
    name: 'MSCEditor Web API',
    version: '1.0.0',
    description: 'My Summer Car存档编辑器Web版本API',
    endpoints: {
      files: '/api/v1/files',
      savegame: '/api/v1/savegame',
      config: '/api/v1/config',
      items: '/api/v1/items'
    },
    documentation: 'https://github.com/your-repo/msc-editor-web'
  });
});

// 错误处理中间件
app.use(notFoundHandler);
app.use(errorHandler);

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 MSCEditor Web Backend 启动成功!`);
  console.log(`📡 服务器运行在: http://localhost:${PORT}`);
  console.log(`📁 上传目录: ${uploadsDir}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

export default app;
