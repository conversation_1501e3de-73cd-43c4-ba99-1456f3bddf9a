// 基于MSCEditor C++代码的类型定义

// 变量类型枚举 (对应C++中的EntryValue::Type)
export enum VariableType {
  Null = 0,
  Transform = 1,
  Float = 2,
  String = 3,
  Boolean = 4,
  Color = 5,
  Integer = 6,
  Vector3 = 7,
  Unknown = 8
}

// 变量类型ID映射 (对应C++中的EntryValue::Ids)
export const VariableTypeIds: Record<VariableType, number> = {
  [VariableType.Null]: 0,
  [VariableType.Transform]: 0x097AFA76,
  [VariableType.Float]: 0x6E3ED76B,
  [VariableType.String]: 0xFDE9F1EE,
  [VariableType.Boolean]: 0xAD4D7C9C,
  [VariableType.Color]: 0x32CF4B31,
  [VariableType.Integer]: 0xE2A80856,
  [VariableType.Vector3]: 0xEC66DC46,
  [VariableType.Unknown]: 0xFFFFFFFF
};

// 变量标志位 (对应C++中的VAR_* 定义)
export enum VariableFlags {
  MODIFIED = 0b1,
  REMOVED = 0b10,
  ADDED = 0b100,
  RENAMED = 0b1000
}

// 文件头常量 (对应C++中的HX_* 定义)
export const FILE_CONSTANTS = {
  HX_STARTENTRY: 0x7E,
  HX_ENDENTRY: 0x7B
} as const;

// 变量头部信息
export interface VariableHeader {
  containerType?: number;
  keyType?: number;
  valueType: number;
  keyProperty?: number;
  valueProperty?: number;
}

// 游戏变量
export interface GameVariable {
  id: string;
  key: string;
  rawKey: string;
  value: string;
  staticValue: string;
  type: VariableType;
  header: VariableHeader;
  group: number;
  position: number;
  flags: number;
  displayValue?: string;
}

// 位置坐标
export interface Position {
  x: number;
  y: number;
  z: number;
}

// 四元数
export interface Quaternion {
  x: number;
  y: number;
  z: number;
  w: number;
}

// 变换信息
export interface Transform {
  position: Position;
  rotation: Quaternion;
  scale: Position;
}

// 传送位置
export interface TeleportLocation {
  name: string;
  displayName: string;
  coordinates: string;
  mapRelated: boolean;
}

// 物品属性
export interface ItemAttribute {
  id: number;
  name: string;
  type: VariableType;
  min?: number;
  max?: number;
}

// 游戏物品
export interface GameItem {
  name: string;
  displayName: string;
  attributes: number[];
  layer: string;
  idName: string;
}

// 汽车零件
export interface CarPart {
  name: string;
  displayName: string;
  installed?: boolean;
  bolts?: number;
  tightness?: number;
  bolted?: boolean;
  damaged?: boolean;
  corner?: string;
}

// 存档文件信息
export interface SaveGameInfo {
  filename: string;
  filepath: string;
  filesize: number;
  lastModified: Date;
  variables: GameVariable[];
  groups: string[];
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 文件上传响应
export interface FileUploadResponse {
  fileId: string;
  filename: string;
  size: number;
  uploadTime: Date;
}

// 变量搜索过滤器
export interface VariableFilter {
  keyword?: string;
  type?: VariableType;
  group?: string;
  modified?: boolean;
  showContainers?: boolean;
}

// 物品生成请求
export interface SpawnItemRequest {
  itemName: string;
  location: string;
  amount: number;
}

// 传送请求
export interface TeleportRequest {
  objectKey: string;
  location: string;
  coordinates?: Position;
}

// 变量修改请求
export interface VariableUpdateRequest {
  value: string;
  type?: VariableType;
}

// 配置文件数据
export interface ConfigData {
  locations: TeleportLocation[];
  items: GameItem[];
  itemAttributes: ItemAttribute[];
  carParts: CarPart[];
  translations: {
    carParts: Record<string, string>;
    vehicles: Record<string, string>;
    teleportObjects: Record<string, string>;
  };
}

// 错误类型
export class ParseError extends Error {
  public code: number;
  public position: number;

  constructor(code: number, position: number, message: string) {
    super(message);
    this.name = 'ParseError';
    this.code = code;
    this.position = position;
  }
}

// 备份信息
export interface BackupInfo {
  filename: string;
  timestamp: Date;
  originalFile: string;
}
