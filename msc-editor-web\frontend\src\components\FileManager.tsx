import React from 'react';
import { Card, Typography, Button } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface FileManagerProps {
  currentFileId: string;
  onFileChange: (fileId: string) => void;
}

const FileManager: React.FC<FileManagerProps> = ({ currentFileId, onFileChange }) => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <FileTextOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }} />
        <Title level={4}>文件管理</Title>
        <Text type="secondary">
          此功能正在开发中，将支持文件备份、恢复和版本管理
        </Text>
        <br />
        <Button type="primary" style={{ marginTop: '16px' }} disabled>
          敬请期待
        </Button>
      </div>
    </Card>
  );
};

export default FileManager;
