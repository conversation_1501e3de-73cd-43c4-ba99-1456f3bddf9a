import React from 'react';
import { Card, Typography, Button } from 'antd';
import { CarOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface TeleportToolProps {
  fileId: string;
}

const TeleportTool: React.FC<TeleportToolProps> = ({ fileId }) => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <CarOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }} />
        <Title level={4}>传送工具</Title>
        <Text type="secondary">
          此功能正在开发中，将支持快速传送到游戏中的各个位置
        </Text>
        <br />
        <Button type="primary" style={{ marginTop: '16px' }} disabled>
          敬请期待
        </Button>
      </div>
    </Card>
  );
};

export default TeleportTool;
