import fs from 'fs';
import path from 'path';
import { 
  GameVariable, 
  VariableType, 
  FILE_CONSTANTS 
} from '../../../shared/types';

/**
 * MSC存档写入器
 * 基于C++版本的SaveFile函数实现
 */
export class SavegameWriter {

  /**
   * 写入存档文件
   */
  public writeSavegame(filePath: string, variables: GameVariable[]): void {
    // 创建备份
    this.createBackup(filePath);

    // 构建二进制数据
    const buffer = this.buildSavegameBuffer(variables);

    // 写入文件
    fs.writeFileSync(filePath, buffer);
  }

  /**
   * 构建存档文件的二进制数据
   */
  private buildSavegameBuffer(variables: GameVariable[]): Buffer {
    const buffers: Buffer[] = [];

    for (const variable of variables) {
      // 跳过已删除的变量
      if (variable.flags & 2) { // VAR_REMOVED
        continue;
      }

      const entryBuffer = this.buildVariableEntry(variable);
      buffers.push(entryBuffer);
    }

    return Buffer.concat(buffers);
  }

  /**
   * 构建单个变量条目的二进制数据
   */
  private buildVariableEntry(variable: GameVariable): Buffer {
    // 构建头部
    const headerBuffer = this.buildHeader(variable.header);
    
    // 构建值数据
    const valueBuffer = Buffer.from(variable.value, 'binary');
    
    // 构建完整的值部分 (头部 + 值 + 结束标记)
    const fullValueBuffer = Buffer.concat([
      headerBuffer,
      valueBuffer,
      Buffer.from([FILE_CONSTANTS.HX_ENDENTRY])
    ]);

    // 构建键数据
    const keyBuffer = Buffer.from(variable.rawKey, 'utf8');
    
    // 构建完整条目
    const entryBuffer = Buffer.concat([
      Buffer.from([FILE_CONSTANTS.HX_STARTENTRY]), // 起始标记
      Buffer.from([keyBuffer.length]),              // 键长度
      keyBuffer,                                    // 键数据
      this.writeUInt32LE(fullValueBuffer.length),   // 值长度
      fullValueBuffer                               // 值数据
    ]);

    return entryBuffer;
  }

  /**
   * 构建变量头部
   */
  private buildHeader(header: any): Buffer {
    const buffers: Buffer[] = [];

    // 容器类型 (如果存在)
    if (header.containerType !== undefined) {
      buffers.push(this.writeUInt32LE(header.containerType));
    }

    // 分隔符
    buffers.push(Buffer.from([0xFF]));

    // 键类型 (如果存在)
    if (header.keyType !== undefined) {
      buffers.push(this.writeUInt32LE(header.keyType));
    }

    // 值类型
    if (header.valueType !== undefined) {
      buffers.push(this.writeUInt32LE(header.valueType));
    }

    return Buffer.concat(buffers);
  }

  /**
   * 将值转换为二进制格式
   */
  public convertValueToBinary(value: string, type: VariableType): string {
    try {
      switch (type) {
        case VariableType.Integer:
          const intValue = parseInt(value);
          const intBuffer = Buffer.allocUnsafe(4);
          intBuffer.writeInt32LE(intValue, 0);
          return intBuffer.toString('binary');

        case VariableType.Float:
          const floatValue = parseFloat(value);
          const floatBuffer = Buffer.allocUnsafe(4);
          floatBuffer.writeFloatLE(floatValue, 0);
          return floatBuffer.toString('binary');

        case VariableType.Boolean:
          const boolValue = value.toLowerCase() === 'true' || value === '1';
          return Buffer.from([boolValue ? 1 : 0]).toString('binary');

        case VariableType.String:
          return Buffer.from(value, 'utf8').toString('binary');

        case VariableType.Transform:
          // Transform格式: 10个float值 (position.x,y,z, rotation.x,y,z,w, scale.x,y,z)
          const transformValues = value.split(',').map(v => parseFloat(v.trim()));
          if (transformValues.length !== 10) {
            throw new Error('Transform需要10个数值');
          }
          const transformBuffer = Buffer.allocUnsafe(40); // 10 * 4 bytes
          for (let i = 0; i < 10; i++) {
            transformBuffer.writeFloatLE(transformValues[i], i * 4);
          }
          return transformBuffer.toString('binary');

        case VariableType.Vector3:
          // Vector3格式: 3个float值 (x, y, z)
          const vector3Values = value.split(',').map(v => parseFloat(v.trim()));
          if (vector3Values.length !== 3) {
            throw new Error('Vector3需要3个数值');
          }
          const vector3Buffer = Buffer.allocUnsafe(12); // 3 * 4 bytes
          for (let i = 0; i < 3; i++) {
            vector3Buffer.writeFloatLE(vector3Values[i], i * 4);
          }
          return vector3Buffer.toString('binary');

        case VariableType.Color:
          // Color格式: 4个float值 (r, g, b, a)
          const colorValues = value.split(',').map(v => parseFloat(v.trim()));
          if (colorValues.length !== 4) {
            throw new Error('Color需要4个数值');
          }
          const colorBuffer = Buffer.allocUnsafe(16); // 4 * 4 bytes
          for (let i = 0; i < 4; i++) {
            colorBuffer.writeFloatLE(colorValues[i], i * 4);
          }
          return colorBuffer.toString('binary');

        default:
          // 对于未知类型，尝试直接使用原始值
          return value;
      }
    } catch (error) {
      throw new Error(`值转换失败: ${error.message}`);
    }
  }

  /**
   * 创建文件备份
   */
  private createBackup(filePath: string): void {
    try {
      const directory = path.dirname(filePath);
      const filename = path.basename(filePath, path.extname(filePath));
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(directory, `${filename}_backup_${timestamp}.bak`);
      
      fs.copyFileSync(filePath, backupPath);
    } catch (error) {
      console.warn('创建备份失败:', error.message);
      // 不抛出错误，允许继续保存
    }
  }

  /**
   * 写入32位小端整数
   */
  private writeUInt32LE(value: number): Buffer {
    const buffer = Buffer.allocUnsafe(4);
    buffer.writeUInt32LE(value, 0);
    return buffer;
  }

  /**
   * 验证变量值格式
   */
  public validateValue(value: string, type: VariableType): boolean {
    try {
      switch (type) {
        case VariableType.Integer:
          return !isNaN(parseInt(value));

        case VariableType.Float:
          return !isNaN(parseFloat(value));

        case VariableType.Boolean:
          return ['true', 'false', '0', '1'].includes(value.toLowerCase());

        case VariableType.String:
          return true; // 字符串总是有效的

        case VariableType.Transform:
          const transformValues = value.split(',');
          return transformValues.length === 10 && 
                 transformValues.every(v => !isNaN(parseFloat(v.trim())));

        case VariableType.Vector3:
          const vector3Values = value.split(',');
          return vector3Values.length === 3 && 
                 vector3Values.every(v => !isNaN(parseFloat(v.trim())));

        case VariableType.Color:
          const colorValues = value.split(',');
          return colorValues.length === 4 && 
                 colorValues.every(v => !isNaN(parseFloat(v.trim())));

        default:
          return true; // 对于未知类型，假设有效
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * 格式化值以供显示
   */
  public formatValueForDisplay(value: string, type: VariableType): string {
    try {
      switch (type) {
        case VariableType.Transform:
          const transformValues = value.split(',').map(v => parseFloat(v.trim()).toFixed(3));
          return `Transform(${transformValues.join(', ')})`;

        case VariableType.Vector3:
          const vector3Values = value.split(',').map(v => parseFloat(v.trim()).toFixed(3));
          return `Vector3(${vector3Values.join(', ')})`;

        case VariableType.Color:
          const colorValues = value.split(',').map(v => parseFloat(v.trim()).toFixed(3));
          return `Color(${colorValues.join(', ')})`;

        case VariableType.Float:
          return parseFloat(value).toFixed(6);

        default:
          return value;
      }
    } catch (error) {
      return value;
    }
  }
}
