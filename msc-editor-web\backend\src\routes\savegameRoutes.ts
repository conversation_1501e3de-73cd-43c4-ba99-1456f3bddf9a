import express from 'express';
import { SavegameController } from '../controllers/SavegameController';

const router = express.Router();
const savegameController = new SavegameController();

// 解析存档文件
router.post('/parse/:fileId', savegameController.parseSavegame);

// 获取变量列表
router.get('/:fileId/variables', savegameController.getVariables);

// 获取单个变量
router.get('/:fileId/variables/:variableId', savegameController.getVariable);

// 修改变量
router.put('/:fileId/variables/:variableId', savegameController.updateVariable);

// 添加变量
router.post('/:fileId/variables', savegameController.addVariable);

// 删除变量
router.delete('/:fileId/variables/:variableId', savegameController.deleteVariable);

// 搜索变量
router.post('/:fileId/variables/search', savegameController.searchVariables);

// 获取变量分组
router.get('/:fileId/groups', savegameController.getGroups);

// 保存存档文件
router.post('/:fileId/save', savegameController.saveSavegame);

// 创建备份
router.post('/:fileId/backup', savegameController.createBackup);

// 获取备份列表
router.get('/:fileId/backups', savegameController.getBackups);

// 恢复备份
router.post('/:fileId/restore/:backupId', savegameController.restoreBackup);

export default router;
