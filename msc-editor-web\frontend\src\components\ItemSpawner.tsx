import React from 'react';
import { Card, Typography, Button } from 'antd';
import { RocketOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ItemSpawnerProps {
  fileId: string;
}

const ItemSpawner: React.FC<ItemSpawnerProps> = ({ fileId }) => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <RocketOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }} />
        <Title level={4}>物品生成功能</Title>
        <Text type="secondary">
          此功能正在开发中，将支持在游戏中生成各种物品
        </Text>
        <br />
        <Button type="primary" style={{ marginTop: '16px' }} disabled>
          敬请期待
        </Button>
      </div>
    </Card>
  );
};

export default ItemSpawner;
