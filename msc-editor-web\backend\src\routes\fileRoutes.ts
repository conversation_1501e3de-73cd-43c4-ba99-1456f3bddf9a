import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { FileController } from '../controllers/FileController';

const router = express.Router();

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名，保留原始扩展名
    const uniqueId = uuidv4();
    const ext = path.extname(file.originalname);
    const filename = `${uniqueId}${ext}`;
    cb(null, filename);
  }
});

// 文件过滤器 - 只允许特定类型的文件
const fileFilter = (req: any, file: any, cb: any) => {
  // 允许的文件扩展名 (MSC存档文件通常没有扩展名或者是.txt)
  const allowedExts = ['', '.txt', '.bak'];
  const ext = path.extname(file.originalname).toLowerCase();
  
  if (allowedExts.includes(ext) || !ext) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型。请上传MSC存档文件。'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB限制
    files: 1 // 一次只能上传一个文件
  }
});

const fileController = new FileController();

// 文件上传
router.post('/upload', upload.single('savefile'), fileController.uploadFile);

// 获取文件列表
router.get('/', fileController.getFiles);

// 获取文件信息
router.get('/:fileId', fileController.getFileInfo);

// 下载文件
router.get('/:fileId/download', fileController.downloadFile);

// 删除文件
router.delete('/:fileId', fileController.deleteFile);

// 文件验证 - 检查是否为有效的MSC存档文件
router.post('/:fileId/validate', fileController.validateFile);

export default router;
