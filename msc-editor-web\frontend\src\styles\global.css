/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

/* 应用容器 */
.app-container {
  min-height: 100vh;
}

/* 主内容区域 */
.main-content {
  padding: 24px;
  min-height: calc(100vh - 64px - 70px); /* 减去header和footer高度 */
}

/* 内容卡片 */
.content-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  flex-shrink: 0;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.upload-icon {
  font-size: 48px;
  color: #1890ff;
}

.upload-text {
  margin: 16px 0 8px;
  font-size: 16px;
  color: #666;
}

.upload-hint {
  color: #999;
  font-size: 14px;
}

/* 加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* 变量表格 */
.variable-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.variable-key {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 2px 4px;
}

.variable-value {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
  
  .variable-key {
    font-size: 10px;
  }
  
  .variable-value {
    max-width: 100px;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格优化 */
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 按钮组优化 */
.ant-space-item .ant-btn {
  border-radius: 6px;
}

/* 标签优化 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 卡片标题优化 */
.ant-card-head-title {
  font-weight: 600;
}

/* 模态框优化 */
.ant-modal-header {
  border-radius: 8px 8px 0 0;
}

.ant-modal-content {
  border-radius: 8px;
}

/* 输入框优化 */
.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector {
  border-radius: 6px;
}

/* 页脚优化 */
.ant-layout-footer {
  background-color: #f0f2f5;
}
