@echo off
chcp 65001 >nul
echo ========================================
echo    MSCEditor Web版本 启动脚本
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

echo 正在检查项目依赖...
if not exist "backend\node_modules" (
    echo 📦 安装后端依赖...
    cd backend
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

if not exist "frontend\node_modules" (
    echo 📦 安装前端依赖...
    cd frontend
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

echo ✅ 项目依赖检查完成
echo.

echo 🚀 启动MSCEditor Web服务...
echo.
echo 后端服务将在 http://localhost:3001 启动
echo 前端服务将在 http://localhost:3000 启动
echo.
echo 请等待服务启动完成后，浏览器会自动打开...
echo 如果浏览器没有自动打开，请手动访问: http://localhost:3000
echo.
echo 按 Ctrl+C 可以停止服务
echo ========================================
echo.

:: 启动后端服务 (后台运行)
start "MSCEditor Backend" cmd /c "cd backend && npm run dev"

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 启动前端服务
cd frontend
call npm start

pause
