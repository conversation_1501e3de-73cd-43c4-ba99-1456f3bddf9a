import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { 
  ApiResponse, 
  SaveGameInfo, 
  GameVariable, 
  VariableFilter,
  VariableUpdateRequest,
  BackupInfo 
} from '../../../shared/types';
import { SavegameParser } from '../services/SavegameParser';
import { SavegameWriter } from '../services/SavegameWriter';

export class SavegameController {
  private uploadsDir = path.join(__dirname, '../../uploads');
  private backupsDir = path.join(__dirname, '../../backups');

  constructor() {
    // 确保备份目录存在
    if (!fs.existsSync(this.backupsDir)) {
      fs.mkdirSync(this.backupsDir, { recursive: true });
    }
  }

  /**
   * 解析存档文件
   */
  public parseSavegame = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const filePath = this.findFileById(fileId);

      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const parser = new SavegameParser(filePath);
      const saveInfo = parser.parse();

      res.json({
        success: true,
        data: saveInfo,
        message: `成功解析存档，包含 ${saveInfo.variables.length} 个变量`
      } as ApiResponse<SaveGameInfo>);

    } catch (error) {
      console.error('解析存档错误:', error);
      res.status(500).json({
        success: false,
        error: `解析存档失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 获取变量列表
   */
  public getVariables = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const { page = 1, pageSize = 50, keyword, type, group } = req.query;
      
      const filePath = this.findFileById(fileId);
      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const parser = new SavegameParser(filePath);
      const saveInfo = parser.parse();
      let variables = saveInfo.variables;

      // 应用过滤器
      if (keyword) {
        const searchTerm = (keyword as string).toLowerCase();
        variables = variables.filter(v => 
          v.key.toLowerCase().includes(searchTerm) ||
          v.displayValue?.toLowerCase().includes(searchTerm)
        );
      }

      if (type) {
        variables = variables.filter(v => v.type === parseInt(type as string));
      }

      if (group) {
        variables = variables.filter(v => v.rawKey === group);
      }

      // 分页
      const startIndex = (parseInt(page as string) - 1) * parseInt(pageSize as string);
      const endIndex = startIndex + parseInt(pageSize as string);
      const paginatedVariables = variables.slice(startIndex, endIndex);

      res.json({
        success: true,
        data: {
          variables: paginatedVariables,
          total: variables.length,
          page: parseInt(page as string),
          pageSize: parseInt(pageSize as string),
          totalPages: Math.ceil(variables.length / parseInt(pageSize as string))
        },
        message: `找到 ${variables.length} 个变量`
      } as ApiResponse);

    } catch (error) {
      console.error('获取变量列表错误:', error);
      res.status(500).json({
        success: false,
        error: `获取变量列表失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 获取单个变量
   */
  public getVariable = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId, variableId } = req.params;
      
      const filePath = this.findFileById(fileId);
      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const parser = new SavegameParser(filePath);
      const saveInfo = parser.parse();
      const variable = saveInfo.variables.find(v => v.id === variableId);

      if (!variable) {
        res.status(404).json({
          success: false,
          error: '变量不存在'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: variable
      } as ApiResponse<GameVariable>);

    } catch (error) {
      console.error('获取变量错误:', error);
      res.status(500).json({
        success: false,
        error: `获取变量失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 修改变量
   */
  public updateVariable = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId, variableId } = req.params;
      const updateData: VariableUpdateRequest = req.body;
      
      const filePath = this.findFileById(fileId);
      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      // 创建备份
      await this.createBackupInternal(fileId);

      // 解析存档
      const parser = new SavegameParser(filePath);
      const saveInfo = parser.parse();
      
      // 查找并修改变量
      const variableIndex = saveInfo.variables.findIndex(v => v.id === variableId);
      if (variableIndex === -1) {
        res.status(404).json({
          success: false,
          error: '变量不存在'
        } as ApiResponse);
        return;
      }

      // 更新变量值
      const variable = saveInfo.variables[variableIndex];
      const writer = new SavegameWriter();
      const newValue = writer.convertValueToBinary(updateData.value, variable.type);
      
      variable.value = newValue;
      variable.displayValue = updateData.value;
      variable.flags |= 1; // 标记为已修改

      // 保存文件
      writer.writeSavegame(filePath, saveInfo.variables);

      res.json({
        success: true,
        data: variable,
        message: '变量修改成功'
      } as ApiResponse<GameVariable>);

    } catch (error) {
      console.error('修改变量错误:', error);
      res.status(500).json({
        success: false,
        error: `修改变量失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 添加变量
   */
  public addVariable = async (req: Request, res: Response): Promise<void> => {
    try {
      // TODO: 实现添加变量功能
      res.status(501).json({
        success: false,
        error: '添加变量功能尚未实现'
      } as ApiResponse);
    } catch (error) {
      console.error('添加变量错误:', error);
      res.status(500).json({
        success: false,
        error: `添加变量失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 删除变量
   */
  public deleteVariable = async (req: Request, res: Response): Promise<void> => {
    try {
      // TODO: 实现删除变量功能
      res.status(501).json({
        success: false,
        error: '删除变量功能尚未实现'
      } as ApiResponse);
    } catch (error) {
      console.error('删除变量错误:', error);
      res.status(500).json({
        success: false,
        error: `删除变量失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 搜索变量
   */
  public searchVariables = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const filter: VariableFilter = req.body;
      
      const filePath = this.findFileById(fileId);
      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const parser = new SavegameParser(filePath);
      const saveInfo = parser.parse();
      let variables = saveInfo.variables;

      // 应用搜索过滤器
      if (filter.keyword) {
        const searchTerm = filter.keyword.toLowerCase();
        variables = variables.filter(v => 
          v.key.toLowerCase().includes(searchTerm) ||
          v.displayValue?.toLowerCase().includes(searchTerm) ||
          v.rawKey.toLowerCase().includes(searchTerm)
        );
      }

      if (filter.type !== undefined) {
        variables = variables.filter(v => v.type === filter.type);
      }

      if (filter.group) {
        variables = variables.filter(v => v.rawKey === filter.group);
      }

      if (filter.modified !== undefined) {
        variables = variables.filter(v => (v.flags & 1) === (filter.modified ? 1 : 0));
      }

      res.json({
        success: true,
        data: variables,
        message: `找到 ${variables.length} 个匹配的变量`
      } as ApiResponse<GameVariable[]>);

    } catch (error) {
      console.error('搜索变量错误:', error);
      res.status(500).json({
        success: false,
        error: `搜索变量失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 获取变量分组
   */
  public getGroups = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      
      const filePath = this.findFileById(fileId);
      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const parser = new SavegameParser(filePath);
      const saveInfo = parser.parse();

      res.json({
        success: true,
        data: saveInfo.groups,
        message: `找到 ${saveInfo.groups.length} 个分组`
      } as ApiResponse<string[]>);

    } catch (error) {
      console.error('获取分组错误:', error);
      res.status(500).json({
        success: false,
        error: `获取分组失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 保存存档文件
   */
  public saveSavegame = async (req: Request, res: Response): Promise<void> => {
    try {
      // TODO: 实现保存存档功能
      res.status(501).json({
        success: false,
        error: '保存存档功能尚未实现'
      } as ApiResponse);
    } catch (error) {
      console.error('保存存档错误:', error);
      res.status(500).json({
        success: false,
        error: `保存存档失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 创建备份
   */
  public createBackup = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const backupInfo = await this.createBackupInternal(fileId);

      res.json({
        success: true,
        data: backupInfo,
        message: '备份创建成功'
      } as ApiResponse<BackupInfo>);

    } catch (error) {
      console.error('创建备份错误:', error);
      res.status(500).json({
        success: false,
        error: `创建备份失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 获取备份列表
   */
  public getBackups = async (req: Request, res: Response): Promise<void> => {
    try {
      // TODO: 实现获取备份列表功能
      res.status(501).json({
        success: false,
        error: '获取备份列表功能尚未实现'
      } as ApiResponse);
    } catch (error) {
      console.error('获取备份列表错误:', error);
      res.status(500).json({
        success: false,
        error: `获取备份列表失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 恢复备份
   */
  public restoreBackup = async (req: Request, res: Response): Promise<void> => {
    try {
      // TODO: 实现恢复备份功能
      res.status(501).json({
        success: false,
        error: '恢复备份功能尚未实现'
      } as ApiResponse);
    } catch (error) {
      console.error('恢复备份错误:', error);
      res.status(500).json({
        success: false,
        error: `恢复备份失败: ${error.message}`
      } as ApiResponse);
    }
  };

  /**
   * 内部方法：创建备份
   */
  private async createBackupInternal(fileId: string): Promise<BackupInfo> {
    const filePath = this.findFileById(fileId);
    if (!filePath) {
      throw new Error('文件不存在');
    }

    const timestamp = new Date();
    const backupId = uuidv4();
    const backupFilename = `${fileId}_${timestamp.getTime()}_${backupId}.bak`;
    const backupPath = path.join(this.backupsDir, backupFilename);

    // 复制文件到备份目录
    fs.copyFileSync(filePath, backupPath);

    return {
      filename: backupFilename,
      timestamp: timestamp,
      originalFile: path.basename(filePath)
    };
  }

  /**
   * 根据文件ID查找文件路径
   */
  private findFileById(fileId: string): string | null {
    try {
      if (!fs.existsSync(this.uploadsDir)) {
        return null;
      }

      const files = fs.readdirSync(this.uploadsDir);
      const targetFile = files.find(filename => {
        const id = path.parse(filename).name;
        return id === fileId;
      });

      return targetFile ? path.join(this.uploadsDir, targetFile) : null;
    } catch (error) {
      console.error('查找文件错误:', error);
      return null;
    }
  }
}
