import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Input, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Select,
  message,
  Tooltip,
  Typography
} from 'antd';
import { 
  SearchOutlined, 
  EditOutlined, 
  SaveOutlined,
  ReloadOutlined,
  FilterOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

interface Variable {
  id: string;
  key: string;
  rawKey: string;
  value: string;
  displayValue: string;
  type: number;
  flags: number;
}

interface VariableEditorProps {
  fileId: string;
}

const VariableEditor: React.FC<VariableEditorProps> = ({ fileId }) => {
  const [variables, setVariables] = useState<Variable[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [typeFilter, setTypeFilter] = useState<number | undefined>();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingVariable, setEditingVariable] = useState<Variable | null>(null);
  const [form] = Form.useForm();

  const variableTypes = {
    0: { name: 'Null', color: 'default' },
    1: { name: 'Transform', color: 'blue' },
    2: { name: 'Float', color: 'green' },
    3: { name: 'String', color: 'orange' },
    4: { name: 'Boolean', color: 'purple' },
    5: { name: 'Color', color: 'pink' },
    6: { name: 'Integer', color: 'cyan' },
    7: { name: 'Vector3', color: 'red' },
    8: { name: 'Unknown', color: 'default' }
  };

  useEffect(() => {
    if (fileId) {
      loadVariables();
    }
  }, [fileId]);

  const loadVariables = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/savegame/${fileId}/variables?pageSize=1000`);
      const result = await response.json();
      
      if (result.success) {
        setVariables(result.data.variables || []);
      } else {
        message.error(result.error || '加载变量失败');
      }
    } catch (error) {
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (variable: Variable) => {
    setEditingVariable(variable);
    form.setFieldsValue({
      key: variable.key,
      value: variable.displayValue,
      type: variable.type
    });
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      if (!editingVariable) return;

      const response = await fetch(`/api/v1/savegame/${fileId}/variables/${editingVariable.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: values.value,
          type: values.type
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        message.success('变量修改成功');
        setEditModalVisible(false);
        loadVariables(); // 重新加载数据
      } else {
        message.error(result.error || '修改失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  const filteredVariables = variables.filter(variable => {
    const matchesSearch = !searchText || 
      variable.key.toLowerCase().includes(searchText.toLowerCase()) ||
      variable.displayValue?.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesType = typeFilter === undefined || variable.type === typeFilter;
    
    return matchesSearch && matchesType;
  });

  const columns: ColumnsType<Variable> = [
    {
      title: '变量名',
      dataIndex: 'key',
      key: 'key',
      width: 200,
      render: (text: string) => (
        <Text code className="variable-key">{text}</Text>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: number) => {
        const typeInfo = variableTypes[type] || variableTypes[8];
        return <Tag color={typeInfo.color}>{typeInfo.name}</Tag>;
      },
    },
    {
      title: '值',
      dataIndex: 'displayValue',
      key: 'displayValue',
      render: (text: string) => (
        <Tooltip title={text}>
          <Text className="variable-value">{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'flags',
      key: 'flags',
      width: 80,
      render: (flags: number) => {
        if (flags & 1) return <Tag color="orange">已修改</Tag>;
        if (flags & 4) return <Tag color="green">新增</Tag>;
        return <Tag color="default">正常</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button 
          type="link" 
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>
      ),
    },
  ];

  return (
    <div>
      {/* 工具栏 */}
      <div className="toolbar">
        <div className="toolbar-left">
          <Space>
            <Search
              placeholder="搜索变量名或值"
              allowClear
              style={{ width: 300 }}
              onSearch={setSearchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
            <Select
              placeholder="筛选类型"
              allowClear
              style={{ width: 120 }}
              onChange={setTypeFilter}
            >
              {Object.entries(variableTypes).map(([type, info]) => (
                <Option key={type} value={parseInt(type)}>
                  {info.name}
                </Option>
              ))}
            </Select>
          </Space>
        </div>
        
        <div className="toolbar-right">
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={loadVariables}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      {/* 变量表格 */}
      <Table
        columns={columns}
        dataSource={filteredVariables}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 50,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个变量`,
        }}
        size="small"
        className="variable-table"
      />

      {/* 编辑模态框 */}
      <Modal
        title="编辑变量"
        open={editModalVisible}
        onOk={handleSave}
        onCancel={() => setEditModalVisible(false)}
        okText="保存"
        cancelText="取消"
        okButtonProps={{ icon: <SaveOutlined /> }}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="变量名"
            name="key"
          >
            <Input disabled />
          </Form.Item>
          
          <Form.Item
            label="类型"
            name="type"
          >
            <Select disabled>
              {Object.entries(variableTypes).map(([type, info]) => (
                <Option key={type} value={parseInt(type)}>
                  {info.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            label="值"
            name="value"
            rules={[{ required: true, message: '请输入变量值' }]}
          >
            <Input.TextArea 
              rows={3}
              placeholder="输入新的变量值"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VariableEditor;
