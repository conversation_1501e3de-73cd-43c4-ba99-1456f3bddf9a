# MSCEditor Web版本

MSCEditor的Web版本，提供与桌面版相同的功能，用户可以通过浏览器访问localhost进行存档编辑。

## 功能特性

- 🌐 Web界面，无需安装桌面软件
- 📁 存档文件上传/下载
- ✏️ 变量编辑和修改
- 🎮 物品生成功能
- 📍 传送功能
- 🇨🇳 完整中文支持
- 💾 自动备份功能
- 🔍 变量搜索和过滤

## 技术架构

### 后端 (Node.js + Express)
- **端口**: 3001
- **API路径**: `/api/v1/`
- **文件处理**: 二进制存档解析
- **配置管理**: 中文配置文件集成

### 前端 (React + TypeScript)
- **端口**: 3000
- **UI框架**: Ant Design
- **状态管理**: React Hooks + Context
- **文件上传**: 拖拽上传支持

## 项目结构

```
msc-editor-web/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── services/        # 业务逻辑
│   │   ├── models/          # 数据模型
│   │   ├── utils/           # 工具函数
│   │   ├── config/          # 配置文件
│   │   └── app.ts           # 应用入口
│   ├── uploads/             # 上传文件临时目录
│   ├── package.json
│   └── tsconfig.json
├── frontend/                # 前端应用
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── utils/           # 工具函数
│   │   ├── types/           # TypeScript类型
│   │   └── App.tsx          # 应用入口
│   ├── public/
│   ├── package.json
│   └── tsconfig.json
├── shared/                  # 共享代码
│   ├── types/               # 共享类型定义
│   └── constants/           # 常量定义
└── docs/                    # 文档
```

## 核心API设计

### 文件管理
- `POST /api/v1/files/upload` - 上传存档文件
- `GET /api/v1/files/download/:id` - 下载存档文件
- `DELETE /api/v1/files/:id` - 删除存档文件

### 存档操作
- `POST /api/v1/savegame/parse` - 解析存档文件
- `GET /api/v1/savegame/variables` - 获取变量列表
- `PUT /api/v1/savegame/variables/:id` - 修改变量
- `POST /api/v1/savegame/variables` - 添加变量
- `DELETE /api/v1/savegame/variables/:id` - 删除变量
- `POST /api/v1/savegame/save` - 保存存档文件

### 功能操作
- `POST /api/v1/items/spawn` - 生成物品
- `POST /api/v1/teleport` - 传送对象
- `GET /api/v1/config/locations` - 获取传送位置
- `GET /api/v1/config/items` - 获取物品列表

## 快速开始

### 安装依赖
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 启动开发服务器
```bash
# 启动后端服务 (端口3001)
cd backend
npm run dev

# 启动前端服务 (端口3000)
cd frontend
npm start
```

### 访问应用
打开浏览器访问: http://localhost:3000

## 与桌面版的对比

| 功能 | 桌面版 | Web版 |
|------|--------|-------|
| 存档解析 | ✅ | ✅ |
| 变量编辑 | ✅ | ✅ |
| 物品生成 | ✅ | ✅ |
| 传送功能 | ✅ | ✅ |
| 地图功能 | ✅ | ❌ (源码未开放) |
| 中文支持 | ✅ | ✅ |
| 文件管理 | 本地文件 | 上传/下载 |
| 跨平台 | Windows | 所有平台 |

## 开发计划

- [x] 项目架构设计
- [ ] 后端API开发
- [ ] 存档解析模块
- [ ] 前端界面开发
- [ ] 功能集成测试
- [ ] 部署和优化

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目基于原MSCEditor项目，遵循相同的开源协议。
