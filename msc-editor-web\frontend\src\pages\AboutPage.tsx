import React from 'react';
import { 
  Card, 
  Typography, 
  Space, 
  Row, 
  Col, 
  Divider,
  Tag,
  List,
  Alert
} from 'antd';
import { 
  InfoCircleOutlined,
  CarOutlined,
  CodeOutlined,
  GithubOutlined,
  BugOutlined,
  HeartOutlined,
  RocketOutlined,
  SafetyOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text, Link } = Typography;

const AboutPage: React.FC = () => {
  const techStack = [
    { name: 'React 18', description: '现代化前端框架' },
    { name: 'TypeScript', description: '类型安全的JavaScript' },
    { name: 'Ant Design', description: '企业级UI设计语言' },
    { name: 'Node.js', description: '高性能JavaScript运行时' },
    { name: 'Express', description: '快速、极简的Web框架' },
    { name: 'Buffer API', description: '二进制数据处理' }
  ];

  const features = [
    '🌐 跨平台Web界面，支持所有现代浏览器',
    '📁 文件上传/下载，支持拖拽操作',
    '✏️ 完整的变量编辑功能',
    '🎮 物品生成和传送功能',
    '🇨🇳 完整的中文支持和本地化',
    '💾 自动备份和版本管理',
    '🔍 强大的搜索和过滤功能',
    '📱 响应式设计，支持移动设备'
  ];

  const changelog = [
    { version: '1.0.0', date: '2024-01-01', changes: ['初始版本发布', '基础存档解析功能', '变量编辑界面'] },
    { version: '1.1.0', date: '2024-01-15', changes: ['添加物品生成功能', '添加传送工具', '优化用户界面'] },
    { version: '1.2.0', date: '2024-02-01', changes: ['完善中文支持', '添加自动备份', '性能优化'] }
  ];

  return (
    <div className="fade-in">
      {/* 项目介绍 */}
      <Card className="content-card" style={{ marginBottom: '24px' }}>
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={16}>
            <Space direction="vertical" size="large">
              <div>
                <Title level={1}>
                  <CarOutlined style={{ color: '#1890ff', marginRight: '12px' }} />
                  MSCEditor Web版本
                </Title>
                <Paragraph style={{ fontSize: '16px' }}>
                  MSCEditor Web版本是基于原版MSCEditor开发的Web应用，
                  为My Summer Car游戏提供完整的存档编辑功能。
                  通过现代化的Web技术，让玩家可以在浏览器中直接编辑游戏存档，
                  无需安装任何软件。
                </Paragraph>
              </div>
              
              <Space wrap>
                <Tag color="blue" icon={<RocketOutlined />}>高性能</Tag>
                <Tag color="green" icon={<SafetyOutlined />}>安全可靠</Tag>
                <Tag color="orange" icon={<CodeOutlined />}>开源项目</Tag>
                <Tag color="purple" icon={<HeartOutlined />}>免费使用</Tag>
              </Space>
            </Space>
          </Col>
          
          <Col xs={24} md={8}>
            <div style={{ textAlign: 'center' }}>
              <InfoCircleOutlined style={{ fontSize: '120px', color: '#1890ff', opacity: 0.8 }} />
            </div>
          </Col>
        </Row>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 功能特性 */}
        <Col xs={24} lg={12}>
          <Card className="content-card" style={{ height: '100%' }}>
            <Title level={3}>
              <RocketOutlined /> 功能特性
            </Title>
            <List
              size="small"
              dataSource={features}
              renderItem={(item) => (
                <List.Item>
                  <Text>{item}</Text>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 技术栈 */}
        <Col xs={24} lg={12}>
          <Card className="content-card" style={{ height: '100%' }}>
            <Title level={3}>
              <CodeOutlined /> 技术栈
            </Title>
            <List
              size="small"
              dataSource={techStack}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={<Text strong>{item.name}</Text>}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 版本历史 */}
      <Card className="content-card" style={{ marginTop: '24px' }}>
        <Title level={3}>
          <InfoCircleOutlined /> 版本历史
        </Title>
        
        {changelog.map((version, index) => (
          <div key={index}>
            <Space align="start" style={{ width: '100%', marginBottom: '16px' }}>
              <Tag color="blue">v{version.version}</Tag>
              <Text type="secondary">{version.date}</Text>
            </Space>
            <List
              size="small"
              dataSource={version.changes}
              renderItem={(change) => (
                <List.Item style={{ paddingLeft: '24px' }}>
                  <Text>• {change}</Text>
                </List.Item>
              )}
            />
            {index < changelog.length - 1 && <Divider />}
          </div>
        ))}
      </Card>

      {/* 使用说明 */}
      <Card className="content-card" style={{ marginTop: '24px' }}>
        <Title level={3}>
          📖 使用说明
        </Title>
        
        <Alert
          message="重要提示"
          description="在使用本工具修改存档前，请务必备份您的原始存档文件。虽然本工具会自动创建备份，但手动备份是最安全的做法。"
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <Paragraph>
          <Title level={4}>基本使用流程：</Title>
          <ol>
            <li>在首页上传您的MSC存档文件</li>
            <li>等待系统解析存档文件</li>
            <li>在变量编辑页面修改需要的数值</li>
            <li>使用物品生成功能添加游戏物品</li>
            <li>使用传送工具快速移动位置</li>
            <li>完成编辑后下载修改后的存档</li>
            <li>将文件替换回游戏目录</li>
          </ol>
        </Paragraph>

        <Paragraph>
          <Title level={4}>存档文件位置：</Title>
          <Text code>
            [游戏安装目录]\SaveGames\
          </Text>
        </Paragraph>
      </Card>

      {/* 联系信息 */}
      <Card className="content-card" style={{ marginTop: '24px' }}>
        <Title level={3}>
          <GithubOutlined /> 开源信息
        </Title>
        
        <Paragraph>
          本项目基于原版MSCEditor开发，采用现代Web技术重新实现。
          项目完全开源，欢迎贡献代码和提出建议。
        </Paragraph>

        <Space size="large">
          <Space>
            <GithubOutlined />
            <Link href="#" target="_blank">
              GitHub仓库
            </Link>
          </Space>
          <Space>
            <BugOutlined />
            <Link href="#" target="_blank">
              问题反馈
            </Link>
          </Space>
          <Space>
            <InfoCircleOutlined />
            <Link href="#" target="_blank">
              使用文档
            </Link>
          </Space>
        </Space>

        <Divider />

        <Text type="secondary" style={{ fontSize: '12px' }}>
          MSCEditor Web版本 v1.2.0 | 
          基于原版MSCEditor | 
          My Summer Car存档编辑器 | 
          © 2024 开源项目
        </Text>
      </Card>
    </div>
  );
};

export default AboutPage;
