import express from 'express';
import { ConfigController } from '../controllers/ConfigController';

const router = express.Router();
const configController = new ConfigController();

// 获取传送位置列表
router.get('/locations', configController.getLocations);

// 获取物品列表
router.get('/items', configController.getItems);

// 获取物品属性
router.get('/item-attributes', configController.getItemAttributes);

// 获取汽车零件列表
router.get('/car-parts', configController.getCarParts);

// 获取翻译数据
router.get('/translations', configController.getTranslations);

// 获取完整配置
router.get('/all', configController.getAllConfig);

export default router;
