{"version": 3, "file": "consistent-type-definitions.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-type-definitions.ts"], "names": [], "mappings": ";;AACA,oDAA2E;AAC3E,wEAG+C;AAE/C,kCAAqC;AAErC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,2EAA2E;YAC7E,WAAW,EAAE,WAAW;SACzB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,yCAAyC;YAC5D,iBAAiB,EAAE,yCAAyC;SAC7D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B;SACF;QACD,OAAO,EAAE,MAAM;KAChB;IACD,cAAc,EAAE,CAAC,WAAW,CAAC;IAC7B,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C;;;WAGG;QACH,SAAS,+CAA+C;YACtD,OAAO,IAAA,2BAAY,EAAC,OAAO,CAAC,CAAC,IAAI,CAC/B,IAAI,CAAC,EAAE,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,IAAI,CAAC,OAAO;gBACZ,IAAI,CAAC,MAAM,CACd,CAAC;QACJ,CAAC;QAED,OAAO;YACL,GAAG,CAAC,MAAM,KAAK,WAAW,IAAI;gBAC5B,6DAA6D,CAC3D,IAAqC;oBAErC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,IAAI,CAAC,EAAE;wBACb,SAAS,EAAE,mBAAmB;wBAC9B,GAAG,CAAC,KAAK;4BACP,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,EAAE,CAAC;4BAChD,MAAM,KAAK,GAAuB,EAAE,CAAC;4BAErC,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BACtD,IAAI,UAAU,EAAE,CAAC;gCACf,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;gCACvD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,gBAAgB,CACpB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACjD,GAAG,CACJ,CACF,CAAC;4BACJ,CAAC;4BAED,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;4BACjE,IACE,UAAU;gCACV,UAAU,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;gCAC9C,UAAU,CAAC,KAAK,KAAK,GAAG,EACxB,CAAC;gCACD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;4BACvC,CAAC;4BAED,OAAO,KAAK,CAAC;wBACf,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;YACF,GAAG,CAAC,MAAM,KAAK,MAAM,IAAI;gBACvB,sBAAsB,CAAC,IAAI;oBACzB,MAAM,GAAG,GAAG,+CAA+C,EAAE;wBAC3D,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,CAAC,KAAyB,EAAsB,EAAE;4BAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,EAAE,CAAC;4BAChD,MAAM,KAAK,GAAuB,EAAE,CAAC;4BAErC,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BACtD,IAAI,UAAU,EAAE,CAAC;gCACf,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;gCAClD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,gBAAgB,CACpB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACvC,KAAK,CACN,CACF,CAAC;4BACJ,CAAC;4BAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gCAC9B,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gCACpD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,cAAc,EAAE,CAAC,CACzD,CAAC;4BACJ,CAAC,CAAC,CAAC;4BAEH,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAC5D,CAAC;gCACD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACxD,KAAK,CAAC,eAAe,CACnB,IAAI,CAAC,IAAI,EACT,oBAAoB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CACnC,CACF,CAAC;4BACJ,CAAC;4BAED,OAAO,KAAK,CAAC;wBACf,CAAC,CAAC;oBACN,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,IAAI,CAAC,EAAE;wBACb,SAAS,EAAE,mBAAmB;wBAC9B;;;2BAGG;wBACH,GAAG;qBACJ,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}