import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import { ApiResponse, FileUploadResponse } from '../../../shared/types';
import { SavegameParser } from '../services/SavegameParser';

export class FileController {
  private uploadsDir = path.join(__dirname, '../../uploads');

  /**
   * 上传存档文件
   */
  public uploadFile = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          error: '没有上传文件'
        } as ApiResponse);
        return;
      }

      const file = req.file;
      const fileId = path.parse(file.filename).name; // 去掉扩展名作为ID

      // 验证文件是否为有效的MSC存档
      try {
        const parser = new SavegameParser(file.path);
        parser.parse(); // 尝试解析以验证文件格式
      } catch (error) {
        // 删除无效文件
        fs.unlinkSync(file.path);
        res.status(400).json({
          success: false,
          error: `无效的存档文件: ${error.message}`
        } as ApiResponse);
        return;
      }

      const response: FileUploadResponse = {
        fileId: fileId,
        filename: file.originalname,
        size: file.size,
        uploadTime: new Date()
      };

      res.json({
        success: true,
        data: response,
        message: '文件上传成功'
      } as ApiResponse<FileUploadResponse>);

    } catch (error) {
      console.error('文件上传错误:', error);
      res.status(500).json({
        success: false,
        error: '文件上传失败'
      } as ApiResponse);
    }
  };

  /**
   * 获取已上传的文件列表
   */
  public getFiles = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!fs.existsSync(this.uploadsDir)) {
        res.json({
          success: true,
          data: [],
          message: '暂无上传文件'
        } as ApiResponse);
        return;
      }

      const files = fs.readdirSync(this.uploadsDir);
      const fileList = files.map(filename => {
        const filePath = path.join(this.uploadsDir, filename);
        const stats = fs.statSync(filePath);
        const fileId = path.parse(filename).name;

        return {
          fileId: fileId,
          filename: filename,
          size: stats.size,
          uploadTime: stats.mtime,
          lastModified: stats.mtime
        };
      });

      res.json({
        success: true,
        data: fileList,
        message: `找到 ${fileList.length} 个文件`
      } as ApiResponse);

    } catch (error) {
      console.error('获取文件列表错误:', error);
      res.status(500).json({
        success: false,
        error: '获取文件列表失败'
      } as ApiResponse);
    }
  };

  /**
   * 获取文件详细信息
   */
  public getFileInfo = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const filePath = this.findFileById(fileId);

      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const stats = fs.statSync(filePath);
      const filename = path.basename(filePath);

      const fileInfo = {
        fileId: fileId,
        filename: filename,
        size: stats.size,
        uploadTime: stats.birthtime,
        lastModified: stats.mtime,
        path: filePath
      };

      res.json({
        success: true,
        data: fileInfo
      } as ApiResponse);

    } catch (error) {
      console.error('获取文件信息错误:', error);
      res.status(500).json({
        success: false,
        error: '获取文件信息失败'
      } as ApiResponse);
    }
  };

  /**
   * 下载文件
   */
  public downloadFile = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const filePath = this.findFileById(fileId);

      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      const filename = path.basename(filePath);
      const originalName = req.query.name as string || filename;

      res.setHeader('Content-Disposition', `attachment; filename="${originalName}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

    } catch (error) {
      console.error('文件下载错误:', error);
      res.status(500).json({
        success: false,
        error: '文件下载失败'
      } as ApiResponse);
    }
  };

  /**
   * 删除文件
   */
  public deleteFile = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const filePath = this.findFileById(fileId);

      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      fs.unlinkSync(filePath);

      res.json({
        success: true,
        message: '文件删除成功'
      } as ApiResponse);

    } catch (error) {
      console.error('文件删除错误:', error);
      res.status(500).json({
        success: false,
        error: '文件删除失败'
      } as ApiResponse);
    }
  };

  /**
   * 验证文件是否为有效的MSC存档
   */
  public validateFile = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileId } = req.params;
      const filePath = this.findFileById(fileId);

      if (!filePath) {
        res.status(404).json({
          success: false,
          error: '文件不存在'
        } as ApiResponse);
        return;
      }

      try {
        const parser = new SavegameParser(filePath);
        const saveInfo = parser.parse();

        res.json({
          success: true,
          data: {
            valid: true,
            variableCount: saveInfo.variables.length,
            groupCount: saveInfo.groups.length,
            fileSize: saveInfo.filesize
          },
          message: '文件验证成功'
        } as ApiResponse);

      } catch (parseError) {
        res.json({
          success: true,
          data: {
            valid: false,
            error: parseError.message
          },
          message: '文件格式无效'
        } as ApiResponse);
      }

    } catch (error) {
      console.error('文件验证错误:', error);
      res.status(500).json({
        success: false,
        error: '文件验证失败'
      } as ApiResponse);
    }
  };

  /**
   * 根据文件ID查找文件路径
   */
  private findFileById(fileId: string): string | null {
    try {
      if (!fs.existsSync(this.uploadsDir)) {
        return null;
      }

      const files = fs.readdirSync(this.uploadsDir);
      const targetFile = files.find(filename => {
        const id = path.parse(filename).name;
        return id === fileId;
      });

      return targetFile ? path.join(this.uploadsDir, targetFile) : null;
    } catch (error) {
      console.error('查找文件错误:', error);
      return null;
    }
  }
}
